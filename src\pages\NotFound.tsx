
import React from "react";
import { <PERSON> } from "react-router-dom";

import { <PERSON><PERSON> } from "@/components/ui/button";

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* SEO meta tags for 404 page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "404 - Page Not Found",
            "description": "The page you are looking for does not exist.",
            "url": window.location.href,  // Current URL
            "isPartOf": {
              "@type": "WebSite",
              "name": "CodeTutorPro",
              "url": "https://codetutorpro.com"
            }
          })
        }}
      />

      <main className="flex-1 flex flex-col items-center justify-center p-6 text-center">
        <h1 className="text-6xl font-bold mb-4">404</h1>
        <p className="text-xl text-muted-foreground mb-8">
          The page you're looking for doesn't exist.
        </p>
        <Button asChild>
          <Link to="/">Return to Home</Link>
        </Button>
      </main>
    </div>
  );
};

export default NotFound;
