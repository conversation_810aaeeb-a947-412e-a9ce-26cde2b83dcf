import { useSEO, defaultSEO } from "@/hooks/seo/useSEO";

const Pricing = () => {
  // Apply SEO data for pricing page
  useSEO({
    ...defaultSEO,
    title: "Pricing - Affordable AI Code Tutorial Generation | CodeTutorPro",
    description: "Discover CodeTutorPro's simple, transparent pricing plans. Start with free early access and scale as you grow. Transform unlimited GitHub repos into tutorials with our AI platform.",
    canonical: "https://codetutorpro.com/pricing",
    ogTitle: "Pricing - Affordable AI Code Tutorial Generation | CodeTutorPro",
    ogDescription: "Discover CodeTutorPro's simple, transparent pricing plans. Start with free early access and scale as you grow.",
    ogUrl: "https://codetutorpro.com/pricing",
    twitterTitle: "Pricing - Affordable AI Code Tutorial Generation | CodeTutorPro",
    twitterDescription: "Discover CodeTutorPro's simple, transparent pricing plans. Start with free early access and scale as you grow.",
    keywords: "CodeTutorPro pricing, AI tutorial generation cost, GitHub tutorial pricing, code learning platform pricing, free early access, subscription plans",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      name: "Pricing - CodeTutorPro",
      description: "Pricing plans for CodeTutorPro's AI-powered code tutorial generation platform",
      url: "https://codetutorpro.com/pricing",
      mainEntity: {
        "@type": "Product",
        name: "CodeTutorPro",
        description: "AI-powered platform that transforms GitHub repositories into comprehensive tutorials",
        offers: [
          {
            "@type": "Offer",
            name: "Free Early Access",
            price: "0",
            priceCurrency: "USD",
            availability: "https://schema.org/InStock",
            description: "Free access during early access period"
          }
        ]
      }
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-purple-600 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Simple, Transparent
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Start with free early access and scale as you grow. No hidden fees, 
              no complicated tiers - just powerful AI-driven tutorial generation.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Pricing Plans Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're finalizing our pricing structure to ensure you get the best value for transforming 
                your code repositories into comprehensive learning experiences.
              </p>
            </div>

            {/* Early access highlight */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-8 mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">🎉 Free Early Access</h3>
              <p className="text-lg text-gray-700 mb-6">
                Join our waiting list and get <strong>free access</strong> to CodeTutorPro during our early access period. 
                No credit card required, no time limits.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white p-4 rounded-lg">
                  <div className="font-semibold text-gray-900">✅ Unlimited Repos</div>
                  <div className="text-gray-600">Transform any GitHub repository</div>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <div className="font-semibold text-gray-900">✅ All Features</div>
                  <div className="text-gray-600">Access to complete platform</div>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <div className="font-semibold text-gray-900">✅ Priority Support</div>
                  <div className="text-gray-600">Direct feedback channel</div>
                </div>
              </div>
            </div>

            {/* Future pricing preview */}
            <div className="text-left">
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">What to Expect</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 bg-purple-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">💡 Starter Plan</h4>
                  <p className="text-gray-600">Perfect for individual developers and small projects</p>
                </div>
                <div className="p-6 bg-pink-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">🚀 Pro Plan</h4>
                  <p className="text-gray-600">Advanced features for teams and larger codebases</p>
                </div>
              </div>
            </div>

            <div className="mt-12">
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
              >
                Get Free Early Access
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Pricing;
