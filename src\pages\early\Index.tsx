
import HeroSection from "@/components/index/HeroSection";
import WaitingList from "@/components/index/WaitingList";
import FeaturesPreview from "@/components/index/FeaturesPreview";
import { useSEO, defaultSEO } from "@/hooks/seo/useSEO";
import { generateHomepageStructuredData } from "@/lib/structuredData";

const Index = () => {
  // Apply SEO data for homepage
  useSEO({
    ...defaultSEO,
    structuredData: generateHomepageStructuredData(),
  });

  return (
    <div className="min-h-screen">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Main content with semantic structure */}
      <main id="main-content" role="main">
        {/* Hero Section */}
        <section aria-label="Hero section">
          <HeroSection />
        </section>

        {/* Features Section */}
        <section aria-label="Features and capabilities">
          <FeaturesPreview />
        </section>

        {/* Waiting List Section */}
        <section aria-label="Join waiting list">
          <WaitingList />
        </section>
      </main>

      {/* Footer with proper semantic structure */}
      <footer className="bg-gray-900 text-white py-12" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>

          <nav aria-label="Footer navigation" className="flex justify-center gap-8 mb-8 text-sm text-gray-400">
            <a href="/features" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">Features</a>
            <a href="/pricing" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">Pricing</a>
            <a href="/about" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">About</a>
            <a href="/contact" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">Contact</a>
            <a href="/privacy" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">Privacy Policy</a>
            <a href="/terms" className="hover:text-white transition-colors focus:text-white focus:outline-none focus:ring-2 focus:ring-purple-400 rounded">Terms of Service</a>
          </nav>

          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};
export default Index;