import { useSEO, defaultSEO } from "@/hooks/seo/useSEO";

const Privacy = () => {
  // Apply SEO data for privacy page
  useSEO({
    ...defaultSEO,
    title: "Privacy Policy - How CodeTutorPro Protects Your Data",
    description: "Learn how CodeTutorPro collects, uses, and protects your personal information. Our comprehensive privacy policy explains our commitment to data security and user privacy.",
    canonical: "https://codetutorpro.com/privacy",
    ogTitle: "Privacy Policy - How CodeTutorPro Protects Your Data",
    ogDescription: "Learn how CodeTutorPro collects, uses, and protects your personal information. Our comprehensive privacy policy explains our commitment to data security.",
    ogUrl: "https://codetutorpro.com/privacy",
    twitterTitle: "Privacy Policy - How CodeTutorPro Protects Your Data",
    twitterDescription: "Learn how CodeTutorPro collects, uses, and protects your personal information. Our comprehensive privacy policy explains our commitment to data security.",
    keywords: "CodeTutorPro privacy policy, data protection, user privacy, GDPR compliance, data security, personal information",
    noindex: false, // Privacy policies should be indexed
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      name: "Privacy Policy - CodeTutorPro",
      description: "CodeTutorPro's privacy policy explaining how we collect, use, and protect user data",
      url: "https://codetutorpro.com/privacy",
      dateModified: "2025-01-27",
      publisher: {
        "@type": "Organization",
        name: "CodeTutorPro",
        url: "https://codetutorpro.com"
      }
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gray-900 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-gray-900 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Privacy
              <span className="text-gray-600"> Policy</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Your privacy is important to us. Learn how we collect, use, and protect 
              your personal information when you use CodeTutorPro.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <div className="text-center mb-8">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Privacy Policy Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're finalizing our comprehensive privacy policy to ensure complete transparency 
                about how we handle your data and protect your privacy.
              </p>
            </div>

            {/* Privacy commitments */}
            <div className="text-left space-y-8">
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">🔒 Our Privacy Commitments</h3>
                <p className="text-gray-700 text-lg mb-4">
                  While we finalize our detailed privacy policy, here are our core commitments to protecting your privacy:
                </p>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-gray-600 mr-3 mt-1">✓</span>
                    <span>We will never sell your personal data to third parties</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-gray-600 mr-3 mt-1">✓</span>
                    <span>Your code repositories are processed securely and not stored permanently</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-gray-600 mr-3 mt-1">✓</span>
                    <span>We use industry-standard encryption to protect your data</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-gray-600 mr-3 mt-1">✓</span>
                    <span>You have full control over your data and can request deletion at any time</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-gray-600 mr-3 mt-1">✓</span>
                    <span>We comply with GDPR, CCPA, and other privacy regulations</span>
                  </li>
                </ul>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">📊 Data Collection</h4>
                  <p className="text-gray-600">
                    We only collect data necessary to provide our service, including email for account management
                    and repository URLs for tutorial generation.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🛡️ Data Security</h4>
                  <p className="text-gray-600">
                    All data is encrypted in transit and at rest. We use secure cloud infrastructure
                    with regular security audits and monitoring.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🎯 Data Usage</h4>
                  <p className="text-gray-600">
                    Your data is used solely to provide CodeTutorPro services, improve our AI models,
                    and communicate important updates.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">👤 Your Rights</h4>
                  <p className="text-gray-600">
                    You have the right to access, modify, or delete your data. Contact us anytime
                    to exercise these rights.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">📧 Questions About Privacy?</h3>
                <p className="text-gray-700 mb-4">
                  If you have any questions about how we handle your data or our privacy practices, 
                  please don't hesitate to reach out to us.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center px-6 py-3 bg-gray-900 text-white font-semibold rounded-lg hover:bg-gray-800 transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Email Privacy Team
                  </a>
                  <a 
                    href="/contact" 
                    className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    Contact Us
                  </a>
                </div>
              </div>
            </div>

            <div className="text-center mt-12">
              <p className="text-sm text-gray-500 mb-6">
                Last updated: January 27, 2025 | Effective date: Upon launch
              </p>
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105"
              >
                Back to Home
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Privacy;
