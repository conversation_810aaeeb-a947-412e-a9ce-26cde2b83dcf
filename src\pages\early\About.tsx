import { useSE<PERSON>, defaultSEO } from "@/hooks/seo/useSEO";
import { generateOrganizationLD } from "@/lib/structuredData";

const About = () => {
  // Apply SEO data for about page
  useSEO({
    ...defaultSEO,
    title: "About Us - Revolutionizing Code Learning with AI | CodeTutorPro",
    description: "Learn about CodeTutorPro's mission to democratize programming education through AI. Discover our story, values, and commitment to making code learning accessible to everyone.",
    canonical: "https://codetutorpro.com/about",
    ogTitle: "About Us - Revolutionizing Code Learning with AI | CodeTutorPro",
    ogDescription: "Learn about CodeTutorPro's mission to democratize programming education through AI. Discover our story, values, and commitment to making code learning accessible.",
    ogUrl: "https://codetutorpro.com/about",
    twitterTitle: "About Us - Revolutionizing Code Learning with AI | CodeTutorPro",
    twitterDescription: "Learn about CodeTutorPro's mission to democratize programming education through AI. Discover our story, values, and commitment to making code learning accessible.",
    keywords: "CodeTutorPro about, AI education mission, programming learning platform, code education democratization, AI tutorial generation company, developer education",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "AboutPage",
      name: "About CodeTutorPro",
      description: "Learn about CodeTutorPro's mission to revolutionize programming education through AI-powered tutorial generation",
      url: "https://codetutorpro.com/about",
      mainEntity: generateOrganizationLD()
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gray-900 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-gray-900 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              About
              <span className="text-gray-600"> CodeTutorPro</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're on a mission to democratize programming education by transforming complex 
              codebases into accessible, interactive learning experiences powered by AI.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Story Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're crafting a compelling story about our journey to revolutionize how developers 
                learn from existing codebases using the power of artificial intelligence.
              </p>
            </div>

            {/* Mission preview */}
            <div className="text-left space-y-8">
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">🎯 Our Mission</h3>
                <p className="text-gray-700 text-lg">
                  To make programming knowledge accessible to everyone by transforming complex GitHub repositories 
                  into comprehensive, beginner-friendly tutorials using advanced AI technology.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🚀 Innovation</h4>
                  <p className="text-gray-600">
                    Leveraging cutting-edge AI to understand and explain code in ways that make sense to learners at every level.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🌍 Accessibility</h4>
                  <p className="text-gray-600">
                    Breaking down barriers to programming education and making quality learning resources available to all.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">📚 Education</h4>
                  <p className="text-gray-600">
                    Transforming how developers learn from existing code by creating structured, interactive tutorials.
                  </p>
                </div>
                <div className="p-6 bg-white border border-gray-200 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🤝 Community</h4>
                  <p className="text-gray-600">
                    Building a community of learners and educators who believe in the power of shared knowledge.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">💡 The Problem We're Solving</h3>
                <p className="text-gray-700 mb-4">
                  Learning from existing codebases is one of the most challenging aspects of becoming a better developer. 
                  Complex repositories can be overwhelming, documentation is often incomplete, and understanding the 
                  relationships between different components requires significant experience.
                </p>
                <p className="text-gray-700">
                  CodeTutorPro bridges this gap by using AI to analyze code structure, identify key concepts, 
                  and generate step-by-step tutorials that guide learners through understanding real-world projects.
                </p>
              </div>
            </div>

            <div className="mt-12">
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105"
              >
                Join Our Mission
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default About;
