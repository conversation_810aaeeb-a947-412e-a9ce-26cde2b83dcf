import { useSE<PERSON>, defaultSE<PERSON> } from "@/hooks/seo/useSEO";
import { generateSoftwareApplicationLD } from "@/lib/structuredData";

const Features = () => {
  // Apply SEO data for features page
  useSEO({
    ...defaultSEO,
    title: "Features - AI-Powered Code Tutorial Generation | CodeTutorPro",
    description: "Discover CodeTutorPro's powerful features: AI code analysis, interactive tutorials, multi-language support, and beginner-friendly explanations. Transform any GitHub repo into learning content.",
    canonical: "https://codetutorpro.com/features",
    ogTitle: "Features - AI-Powered Code Tutorial Generation | CodeTutorPro",
    ogDescription: "Discover CodeTutorPro's powerful features: AI code analysis, interactive tutorials, multi-language support, and beginner-friendly explanations.",
    ogUrl: "https://codetutorpro.com/features",
    twitterTitle: "Features - AI-Powered Code Tutorial Generation | CodeTutorPro",
    twitterDescription: "Discover CodeTutorPro's powerful features: AI code analysis, interactive tutorials, multi-language support, and beginner-friendly explanations.",
    keywords: "AI code analysis, interactive tutorials, code learning features, programming education tools, GitHub integration, multi-language support, beginner coding tutorials",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      name: "Features - CodeTutorPro",
      description: "Comprehensive features of CodeTutorPro's AI-powered code tutorial generation platform",
      url: "https://codetutorpro.com/features",
      mainEntity: generateSoftwareApplicationLD()
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gray-900 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-gray-900 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Powerful Features for
              <span className="text-gray-600"> AI-Driven Learning</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how CodeTutorPro transforms complex codebases into comprehensive, 
              interactive tutorials that make learning programming intuitive and engaging.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Features Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're crafting an amazing features showcase that will demonstrate the full power of CodeTutorPro's 
                AI-driven tutorial generation capabilities.
              </p>
            </div>

            {/* Feature preview list */}
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">🤖 AI Code Analysis</h3>
                <p className="text-gray-600">Advanced AI that understands code structure, patterns, and dependencies</p>
              </div>
              <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">📚 Interactive Tutorials</h3>
                <p className="text-gray-600">Step-by-step tutorials with hands-on exercises and real examples</p>
              </div>
              <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">🌐 Multi-Language Support</h3>
                <p className="text-gray-600">Support for JavaScript, Python, Java, Go, Rust, and more</p>
              </div>
              <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">🎯 Beginner-Friendly</h3>
                <p className="text-gray-600">Complex concepts broken down into digestible learning modules</p>
              </div>
            </div>

            <div className="mt-12">
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105"
              >
                Join the Waiting List
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Features;
