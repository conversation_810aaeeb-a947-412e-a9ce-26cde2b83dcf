import { useSEO, defaultSEO } from "@/hooks/seo/useSEO";

const Terms = () => {
  // Apply SEO data for terms page
  useSEO({
    ...defaultSEO,
    title: "Terms of Service - CodeTutorPro Usage Agreement",
    description: "Read CodeTutorPro's terms of service to understand your rights and responsibilities when using our AI-powered code tutorial generation platform.",
    canonical: "https://codetutorpro.com/terms",
    ogTitle: "Terms of Service - CodeTutorPro Usage Agreement",
    ogDescription: "Read CodeTutorPro's terms of service to understand your rights and responsibilities when using our AI-powered code tutorial generation platform.",
    ogUrl: "https://codetutorpro.com/terms",
    twitterTitle: "Terms of Service - CodeTutorPro Usage Agreement",
    twitterDescription: "Read CodeTutorPro's terms of service to understand your rights and responsibilities when using our AI-powered platform.",
    keywords: "CodeTutorPro terms of service, usage agreement, terms and conditions, user agreement, legal terms, service terms",
    noindex: false, // Terms should be indexed
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      name: "Terms of Service - CodeTutorPro",
      description: "CodeTutorPro's terms of service outlining user rights and responsibilities",
      url: "https://codetutorpro.com/terms",
      dateModified: "2025-01-27",
      publisher: {
        "@type": "Organization",
        name: "CodeTutorPro",
        url: "https://codetutorpro.com"
      }
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-purple-600 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Terms of
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"> Service</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These terms govern your use of CodeTutorPro. Please read them carefully 
              to understand your rights and responsibilities.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <div className="text-center mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Terms of Service Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're preparing comprehensive terms of service that clearly outline the rights and 
                responsibilities for using CodeTutorPro's AI-powered tutorial generation platform.
              </p>
            </div>

            {/* Key terms preview */}
            <div className="text-left space-y-8">
              <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">📋 Key Terms Overview</h3>
                <p className="text-gray-700 text-lg mb-4">
                  While we finalize our detailed terms of service, here are the key principles that will guide our agreement:
                </p>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-orange-600 mr-3 mt-1">•</span>
                    <span>Fair use of our AI-powered tutorial generation service</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-600 mr-3 mt-1">•</span>
                    <span>Respect for intellectual property rights of code repositories</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-600 mr-3 mt-1">•</span>
                    <span>Prohibition of misuse, abuse, or harmful activities</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-600 mr-3 mt-1">•</span>
                    <span>Clear guidelines for content ownership and usage rights</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-600 mr-3 mt-1">•</span>
                    <span>Transparent policies for service availability and limitations</span>
                  </li>
                </ul>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 bg-blue-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">✅ Acceptable Use</h4>
                  <p className="text-gray-600">
                    Use CodeTutorPro for legitimate educational purposes, respect repository licenses, 
                    and follow our community guidelines.
                  </p>
                </div>
                <div className="p-6 bg-green-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🔒 Content Rights</h4>
                  <p className="text-gray-600">
                    You retain rights to your original content. Generated tutorials are yours to use 
                    according to the source repository's license.
                  </p>
                </div>
                <div className="p-6 bg-purple-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">⚖️ Service Limits</h4>
                  <p className="text-gray-600">
                    Fair usage policies ensure quality service for all users. Enterprise plans 
                    available for higher volume needs.
                  </p>
                </div>
                <div className="p-6 bg-pink-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">🛡️ Liability</h4>
                  <p className="text-gray-600">
                    Clear limitations on liability while ensuring we provide reliable, 
                    high-quality service to our users.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">❓ Questions About Terms?</h3>
                <p className="text-gray-700 mb-4">
                  If you have questions about our terms of service or need clarification on any policies, 
                  we're here to help. Our goal is to create fair, transparent terms that protect both users and our service.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="inline-flex items-center px-6 py-3 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Email Legal Team
                  </a>
                  <a 
                    href="/contact" 
                    className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    General Contact
                  </a>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                <div className="flex items-start">
                  <svg className="w-6 h-6 text-yellow-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <h4 className="font-semibold text-yellow-800 mb-2">Early Access Notice</h4>
                    <p className="text-yellow-700">
                      During our early access period, these terms may be updated as we refine our service. 
                      All users will be notified of any changes, and continued use constitutes acceptance of updated terms.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center mt-12">
              <p className="text-sm text-gray-500 mb-6">
                Last updated: January 27, 2025 | Effective date: Upon launch
              </p>
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
              >
                Back to Home
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Terms;
