#!/usr/bin/env node

/**
 * Simple script to test all routes from sitemap.xml
 * This helps verify that all pages are accessible and return 200 status
 */

import http from 'http';

const routes = [
  '/',
  '/features',
  '/pricing',
  '/about',
  '/contact',
  '/privacy',
  '/terms'
];

const baseUrl = 'http://localhost:8081';

async function testRoute(route) {
  return new Promise((resolve) => {
    const url = `${baseUrl}${route}`;
    
    http.get(url, (res) => {
      const status = res.statusCode;
      const success = status === 200;
      
      console.log(`${success ? '✅' : '❌'} ${route} - Status: ${status}`);
      
      resolve({ route, status, success });
    }).on('error', (err) => {
      console.log(`❌ ${route} - Error: ${err.message}`);
      resolve({ route, status: 'ERROR', success: false });
    });
  });
}

async function testAllRoutes() {
  console.log('🧪 Testing all routes from sitemap.xml...\n');
  
  const results = [];
  
  for (const route of routes) {
    const result = await testRoute(route);
    results.push(result);
  }
  
  console.log('\n📊 Summary:');
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Successful: ${successful}/${total}`);
  
  if (successful === total) {
    console.log('🎉 All routes are working correctly!');
  } else {
    console.log('⚠️  Some routes need attention.');
    const failed = results.filter(r => !r.success);
    failed.forEach(r => console.log(`   - ${r.route}: ${r.status}`));
  }
}

// Run the tests
testAllRoutes().catch(console.error);
