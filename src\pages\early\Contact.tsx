import { useSEO, defaultSEO } from "@/hooks/seo/useSEO";

const Contact = () => {
  // Apply SEO data for contact page
  useSEO({
    ...defaultSEO,
    title: "Contact Us - Get in Touch with CodeTutorPro Team",
    description: "Have questions about CodeTutorPro? Get in touch with our team for support, partnerships, or feedback. We're here to help you transform code into learning experiences.",
    canonical: "https://codetutorpro.com/contact",
    ogTitle: "Contact Us - Get in Touch with CodeTutorPro Team",
    ogDescription: "Have questions about CodeTutorPro? Get in touch with our team for support, partnerships, or feedback. We're here to help you transform code into learning experiences.",
    ogUrl: "https://codetutorpro.com/contact",
    twitterTitle: "Contact Us - Get in Touch with CodeTutorPro Team",
    twitterDescription: "Have questions about CodeTutorPro? Get in touch with our team for support, partnerships, or feedback.",
    keywords: "CodeTutorPro contact, customer support, AI tutorial platform support, contact form, get in touch, partnerships, feedback",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      name: "Contact CodeTutorPro",
      description: "Get in touch with the CodeTutorPro team for support, partnerships, or feedback",
      url: "https://codetutorpro.com/contact",
      mainEntity: {
        "@type": "Organization",
        name: "CodeTutorPro",
        url: "https://codetutorpro.com",
        contactPoint: {
          "@type": "ContactPoint",
          contactType: "Customer Service",
          email: "<EMAIL>",
          availableLanguage: "English"
        }
      }
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Skip to main content for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gray-900 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold">
                Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
              </h1>
            </div>
            <nav aria-label="Main navigation">
              <a href="/" className="text-gray-600 hover:text-gray-900 transition-colors">← Back to Home</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main id="main-content" role="main" className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Page header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Get in
              <span className="text-gray-600"> Touch</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Have questions about CodeTutorPro? We'd love to hear from you. 
              Reach out for support, partnerships, or just to share your feedback.
            </p>
          </div>

          {/* Coming soon content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact Form Coming Soon</h2>
              <p className="text-lg text-gray-600 mb-8">
                We're building a comprehensive contact system to make it easy for you to reach out. 
                In the meantime, here's how you can connect with us.
              </p>
            </div>

            {/* Contact methods */}
            <div className="grid md:grid-cols-2 gap-8 text-left mb-12">
              <div className="p-6 bg-gray-50 border border-gray-200 rounded-xl">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Email Us</h3>
                </div>
                <p className="text-gray-600 mb-3">
                  For general inquiries, support, or partnerships
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-900 hover:text-gray-700 font-medium"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="p-6 bg-gray-50 border border-gray-200 rounded-xl">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Join the Community</h3>
                </div>
                <p className="text-gray-600 mb-3">
                  Connect with other developers and get updates
                </p>
                <div className="space-y-2">
                  <div className="text-gray-900 hover:text-gray-700 font-medium">Discord (Coming Soon)</div>
                  <div className="text-gray-900 hover:text-gray-700 font-medium">Twitter (Coming Soon)</div>
                </div>
              </div>
            </div>

            {/* FAQ section */}
            <div className="bg-gray-50 rounded-xl p-8 text-left">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">When will CodeTutorPro launch?</h4>
                  <p className="text-gray-600">We're planning to launch in Q2 2025. Join our waiting list for early access!</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">How can I get early access?</h4>
                  <p className="text-gray-600">Simply join our waiting list on the homepage. Early access users get free access to all features.</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Do you offer partnerships?</h4>
                  <p className="text-gray-600">Yes! We're interested in partnerships with educational institutions, coding bootcamps, and developer tools. Email <NAME_EMAIL></p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Can I provide feedback or suggestions?</h4>
                  <p className="text-gray-600">Absolutely! We value community feedback. Reach out via email or join our waiting list to be part of our feedback program.</p>
                </div>
              </div>
            </div>

            <div className="mt-12">
              <a 
                href="/" 
                className="inline-flex items-center px-8 py-3 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105"
              >
                Join the Waiting List
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16" role="contentinfo">
        <div className="container mx-auto px-6 text-center">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Code<span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Tutor</span>Pro
            </h2>
            <p className="text-gray-400">Transforming code into knowledge with AI</p>
          </div>
          <div className="text-gray-500 text-sm">© 2025 CodeTutorPro. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Contact;
